﻿// <auto-generated />
using System;
using DisasterApp.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DisasterApp.Infrastructure.Migrations
{
    [DbContext(typeof(DisasterDbContext))]
    partial class DisasterDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("DisasterApp.Domain.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("AuditLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("audit_log_id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("action");

                    b.Property<string>("EntityId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("entity_id");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("entity_type");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)")
                        .HasColumnName("ip_address");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("new_values");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("old_values");

                    b.Property<DateTime>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("timestamp")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("user_agent");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("user_name");

                    b.HasKey("AuditLogId")
                        .HasName("PK__AuditLog__3213E83F7F7A8BE5");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLog", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Chat", b =>
                {
                    b.Property<int>("ChatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("chat_id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ChatId"));

                    b.Property<string>("AttachmentUrl")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("attachment_url");

                    b.Property<bool?>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_read");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("message");

                    b.Property<Guid>("ReceiverId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("receiver_id");

                    b.Property<Guid>("SenderId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("sender_id");

                    b.Property<DateTime?>("SentAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("sent_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.HasKey("ChatId")
                        .HasName("PK__Chats__FD040B1769A39242");

                    b.HasIndex("ReceiverId");

                    b.HasIndex("SenderId");

                    b.ToTable("Chats");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<int>("DisasterTypeId")
                        .HasColumnType("int")
                        .HasColumnName("disaster_type_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK__Disaster__3213E83F0C630599");

                    b.HasIndex("DisasterTypeId");

                    b.ToTable("DisasterEvent", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterReport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<Guid>("DisasterEventId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("disaster_event_id");

                    b.Property<bool?>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("severity");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("status");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2")
                        .HasColumnName("timestamp");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("verified_at");

                    b.Property<Guid?>("VerifiedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("verified_by");

                    b.HasKey("Id")
                        .HasName("PK__Disaster__3213E83F1C33B9DA");

                    b.HasIndex("DisasterEventId");

                    b.HasIndex("UserId");

                    b.HasIndex("VerifiedBy");

                    b.ToTable("DisasterReport", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("category");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK__Disaster__3213E83F43004DCF");

                    b.HasIndex(new[] { "Name" }, "UQ__Disaster__72E12F1B433397EB")
                        .IsUnique();

                    b.ToTable("DisasterType", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Donation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(12, 2)")
                        .HasColumnName("amount");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("DonationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("donation_type");

                    b.Property<string>("DonorContact")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("donor_contact");

                    b.Property<string>("DonorName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("donor_name");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("organization_id");

                    b.Property<DateTime>("ReceivedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("received_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("Status")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("Pending")
                        .HasColumnName("status");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("verified_at");

                    b.Property<Guid?>("VerifiedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("verified_by");

                    b.HasKey("Id")
                        .HasName("PK__Donation__3213E83F4C3E4EA3");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("UserId");

                    b.HasIndex("VerifiedBy");

                    b.ToTable("Donations");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.ImpactDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<int>("ImpactTypeId")
                        .HasColumnType("int")
                        .HasColumnName("impact_type_id");

                    b.Property<bool?>("IsResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_resolved");

                    b.Property<Guid>("ReportId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("report_id");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("resolved_at");

                    b.Property<string>("Severity")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK__ImpactDe__3213E83F8E740B80");

                    b.HasIndex("ImpactTypeId");

                    b.HasIndex("ReportId");

                    b.ToTable("ImpactDetail", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.ImpactType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK__ImpactTy__3213E83F560AA2C1");

                    b.HasIndex(new[] { "Name" }, "UQ__ImpactTy__72E12F1B4AA4C730")
                        .IsUnique();

                    b.ToTable("ImpactType", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Location", b =>
                {
                    b.Property<Guid>("LocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("location_id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("address");

                    b.Property<string>("CoordinatePrecision")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("coordinate_precision");

                    b.Property<string>("FormattedAddress")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("formatted_address");

                    b.Property<decimal>("Latitude")
                        .HasColumnType("decimal(10, 8)")
                        .HasColumnName("latitude");

                    b.Property<decimal>("Longitude")
                        .HasColumnType("decimal(11, 8)")
                        .HasColumnName("longitude");

                    b.Property<Guid>("ReportId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("report_id");

                    b.HasKey("LocationId")
                        .HasName("PK__Location__771831EA81520751");

                    b.HasIndex(new[] { "ReportId" }, "UQ__Location__779B7C59E4A3E040")
                        .IsUnique();

                    b.ToTable("Location", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Organization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("contact_email");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool?>("IsVerified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_verified");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("logo_url");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<string>("WebsiteUrl")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("website_url");

                    b.HasKey("Id")
                        .HasName("PK__Organiza__3213E83FE53B6B28");

                    b.HasIndex("UserId");

                    b.HasIndex(new[] { "Name" }, "UQ__Organiza__72E12F1B7113F934")
                        .IsUnique();

                    b.ToTable("Organizations");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.PasswordResetToken", b =>
                {
                    b.Property<Guid>("PasswordResetTokenId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("password_reset_token_id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<DateTime>("ExpiredAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("expired_at");

                    b.Property<bool>("IsUsed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_used");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("token");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("PasswordResetTokenId")
                        .HasName("PK__Password__B0A1F7C71F7A268B");

                    b.HasIndex(new[] { "UserId" }, "IX_PasswordResetToken_user_id");

                    b.HasIndex(new[] { "Token" }, "UQ__Password__CA90DA7A24B1CB17")
                        .IsUnique();

                    b.ToTable("PasswordResetToken", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Photo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Caption")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("caption");

                    b.Property<string>("PublicId")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("public_id");

                    b.Property<Guid>("ReportId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("report_id");

                    b.Property<DateTime?>("UploadedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("uploaded_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("url");

                    b.HasKey("Id")
                        .HasName("PK__Photo__3213E83F676C6DC5");

                    b.HasIndex("ReportId");

                    b.ToTable("Photo", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.RefreshToken", b =>
                {
                    b.Property<Guid>("RefreshTokenId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("refresh_token_id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<DateTime>("ExpiredAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("expired_at");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("token");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("RefreshTokenId")
                        .HasName("PK__RefreshT__B0A1F7C71F7A268A");

                    b.HasIndex(new[] { "UserId" }, "IX_RefreshToken_user_id");

                    b.HasIndex(new[] { "Token" }, "UQ__RefreshT__CA90DA7A24B1CB16")
                        .IsUnique();

                    b.ToTable("RefreshToken", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Role", b =>
                {
                    b.Property<Guid>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("role_id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name");

                    b.HasKey("RoleId")
                        .HasName("PK__Role__760965CCCEA47220");

                    b.HasIndex(new[] { "Name" }, "UQ__Role__72E12F1B6DD5B5D7")
                        .IsUnique();

                    b.ToTable("Role", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.SupportRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<Guid>("ReportId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("report_id");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("status");

                    b.Property<int>("SupportTypeId")
                        .HasColumnType("int")
                        .HasColumnName("support_type_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<byte>("Urgency")
                        .HasColumnType("tinyint")
                        .HasColumnName("urgency");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK__SupportR__3213E83F7F7A8BE5");

                    b.HasIndex("ReportId");

                    b.HasIndex("SupportTypeId");

                    b.HasIndex("UserId");

                    b.ToTable("SupportRequest", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.SupportType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK__SupportT__3213E83FC38913DF");

                    b.HasIndex(new[] { "Name" }, "UQ__SupportT__72E12F1B1361FEE0")
                        .IsUnique();

                    b.ToTable("SupportType", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.User", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id")
                        .HasDefaultValueSql("(newid())");

                    b.Property<string>("AuthId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("auth_id");

                    b.Property<string>("AuthProvider")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("auth_provider");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("(sysutcdatetime())");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("email");

                    b.Property<bool?>("IsBlacklisted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_blacklisted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("phone_number");

                    b.Property<string>("PhotoUrl")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("photo_url");

                    b.HasKey("UserId")
                        .HasName("PK__User__B9BE370FF42BE9EA");

                    b.HasIndex(new[] { "Email" }, "IX_User_Email");

                    b.HasIndex(new[] { "AuthProvider", "AuthId" }, "UQ_User_AuthProviderId")
                        .IsUnique();

                    b.HasIndex(new[] { "Email" }, "UQ__User__AB6E61647E5028D0")
                        .IsUnique();

                    b.ToTable("User", (string)null);
                });

            modelBuilder.Entity("UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex(new[] { "RoleId" }, "IX_UserRole_role_id");

                    b.ToTable("UserRole", (string)null);
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.AuditLog", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_AuditLog_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Chat", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.User", "Receiver")
                        .WithMany("ChatReceivers")
                        .HasForeignKey("ReceiverId")
                        .IsRequired()
                        .HasConstraintName("FK_Chat_Receiver");

                    b.HasOne("DisasterApp.Domain.Entities.User", "Sender")
                        .WithMany("ChatSenders")
                        .HasForeignKey("SenderId")
                        .IsRequired()
                        .HasConstraintName("FK_Chat_Sender");

                    b.Navigation("Receiver");

                    b.Navigation("Sender");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterEvent", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.DisasterType", "DisasterType")
                        .WithMany("DisasterEvents")
                        .HasForeignKey("DisasterTypeId")
                        .IsRequired()
                        .HasConstraintName("FK__DisasterE__disas__41B8C09B");

                    b.Navigation("DisasterType");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterReport", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.DisasterEvent", "DisasterEvent")
                        .WithMany("DisasterReports")
                        .HasForeignKey("DisasterEventId")
                        .IsRequired()
                        .HasConstraintName("FK_DisasterReport_DisasterEvent");

                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany("DisasterReportUsers")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_DisasterReport_User");

                    b.HasOne("DisasterApp.Domain.Entities.User", "VerifiedByNavigation")
                        .WithMany("DisasterReportVerifiedByNavigations")
                        .HasForeignKey("VerifiedBy")
                        .HasConstraintName("FK_DisasterReport_VerifiedBy");

                    b.Navigation("DisasterEvent");

                    b.Navigation("User");

                    b.Navigation("VerifiedByNavigation");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Donation", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.Organization", "Organization")
                        .WithMany("Donations")
                        .HasForeignKey("OrganizationId")
                        .IsRequired()
                        .HasConstraintName("FK_Donations_Organization");

                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany("DonationUsers")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_Donations_User");

                    b.HasOne("DisasterApp.Domain.Entities.User", "VerifiedByNavigation")
                        .WithMany("DonationVerifiedByNavigations")
                        .HasForeignKey("VerifiedBy")
                        .HasConstraintName("FK_Donations_VerifiedBy");

                    b.Navigation("Organization");

                    b.Navigation("User");

                    b.Navigation("VerifiedByNavigation");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.ImpactDetail", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.ImpactType", "ImpactType")
                        .WithMany("ImpactDetails")
                        .HasForeignKey("ImpactTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_ImpactDetail_ImpactType");

                    b.HasOne("DisasterApp.Domain.Entities.DisasterReport", "Report")
                        .WithMany("ImpactDetails")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_ImpactDetail_Report");

                    b.Navigation("ImpactType");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Location", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.DisasterReport", "Report")
                        .WithOne("Location")
                        .HasForeignKey("DisasterApp.Domain.Entities.Location", "ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_Location_Report");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Organization", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany("Organizations")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_Organizations_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.PasswordResetToken", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany("PasswordResetTokens")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_PasswordResetToken_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Photo", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.DisasterReport", "Report")
                        .WithMany("Photos")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_Photo_Report");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.RefreshToken", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_RefreshToken_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.SupportRequest", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.DisasterReport", "Report")
                        .WithMany("SupportRequests")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_SupportRequest_Report");

                    b.HasOne("DisasterApp.Domain.Entities.SupportType", "SupportType")
                        .WithMany("SupportRequests")
                        .HasForeignKey("SupportTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_SupportRequest_SupportType");

                    b.HasOne("DisasterApp.Domain.Entities.User", "User")
                        .WithMany("SupportRequests")
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_SupportRequest_User");

                    b.Navigation("Report");

                    b.Navigation("SupportType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("UserRole", b =>
                {
                    b.HasOne("DisasterApp.Domain.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .IsRequired()
                        .HasConstraintName("FK_UserRole_Role");

                    b.HasOne("DisasterApp.Domain.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK_UserRole_User");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterEvent", b =>
                {
                    b.Navigation("DisasterReports");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterReport", b =>
                {
                    b.Navigation("ImpactDetails");

                    b.Navigation("Location");

                    b.Navigation("Photos");

                    b.Navigation("SupportRequests");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.DisasterType", b =>
                {
                    b.Navigation("DisasterEvents");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.ImpactType", b =>
                {
                    b.Navigation("ImpactDetails");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.Organization", b =>
                {
                    b.Navigation("Donations");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.SupportType", b =>
                {
                    b.Navigation("SupportRequests");
                });

            modelBuilder.Entity("DisasterApp.Domain.Entities.User", b =>
                {
                    b.Navigation("ChatReceivers");

                    b.Navigation("ChatSenders");

                    b.Navigation("DisasterReportUsers");

                    b.Navigation("DisasterReportVerifiedByNavigations");

                    b.Navigation("DonationUsers");

                    b.Navigation("DonationVerifiedByNavigations");

                    b.Navigation("Organizations");

                    b.Navigation("PasswordResetTokens");

                    b.Navigation("RefreshTokens");

                    b.Navigation("SupportRequests");
                });
#pragma warning restore 612, 618
        }
    }
}
